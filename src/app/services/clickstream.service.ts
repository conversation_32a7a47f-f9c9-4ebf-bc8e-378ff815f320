import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import {
  ClickstreamEvent,
  ClickstreamEventType,
  ReflectionTimeData,
  FieldInteraction,
  AnnotationSession,
  ClickstreamConfig
} from '../models/clickstream.models';
import { LocalStorageService } from './local-storage.service';

@Injectable({
  providedIn: 'root'
})
export class ClickstreamService {
  private config: ClickstreamConfig = {
    enableTracking: true,
    trackMouseMovements: false,
    mouseMoveThrottleMs: 100,
    batchSize: 50,
    flushIntervalMs: 30000, // 30 seconds
    enableLocalStorage: true,
    apiEndpoint: 'http://localhost:8000/clickstream'
  };

  private events: ClickstreamEvent[] = [];
  private currentSession: AnnotationSession | null = null;
  private sessionId: string = '';
  private userId: string = '';

  // Current annotation tracking
  private currentAnnotationStart: number | null = null;
  private currentFieldInteractions: Map<string, FieldInteraction> = new Map();
  private currentReflectionData: Partial<ReflectionTimeData> | null = null;
  private isTrackingAiSuggestion: boolean = false;

  // Subjects for real-time tracking
  private eventSubject = new Subject<ClickstreamEvent>();
  private reflectionTimeSubject = new Subject<ReflectionTimeData>();

  public events$ = this.eventSubject.asObservable();
  public reflectionTimes$ = this.reflectionTimeSubject.asObservable();

  constructor(private localStorage: LocalStorageService) {
    this.initializeSession();
    this.setupAutoFlush();
  }

  private initializeSession(): void {
    this.sessionId = this.generateId();
    this.userId = this.localStorage.getItem('uniqueId') || 'anonymous';

    const spectroName = this.localStorage.getItem('spectroName') || 'unknown';

    this.currentSession = {
      sessionId: this.sessionId,
      userId: this.userId,
      spectroName: spectroName,
      startTime: Date.now(),
      annotationCount: 0,
      reflectionTimes: [],
      clickstreamEvents: []
    };

    this.trackEvent(ClickstreamEventType.PAGE_LOADED, {
      elementId: 'session',
      metadata: { spectroName }
    });
  }

  private setupAutoFlush(): void {
    setInterval(() => {
      this.flushEvents();
    }, this.config.flushIntervalMs);
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  public trackEvent(
    eventType: ClickstreamEventType,
    options: {
      elementId?: string;
      elementType?: string;
      value?: any;
      metadata?: Record<string, any>;
    } = {}
  ): void {
    if (!this.config.enableTracking) return;

    const event: ClickstreamEvent = {
      id: this.generateId(),
      userId: this.userId,
      sessionId: this.sessionId,
      timestamp: Date.now(),
      eventType,
      ...options
    };

    this.events.push(event);
    this.currentSession?.clickstreamEvents.push(event);
    this.eventSubject.next(event);

    // Auto-flush if batch size reached
    if (this.events.length >= this.config.batchSize) {
      this.flushEvents();
    }
  }

  // Annotation form tracking methods
  public startAnnotationForm(rectangleData?: any): void {
    this.currentAnnotationStart = Date.now();
    this.currentFieldInteractions.clear();

    // Check if this is an AI suggestion
    this.isTrackingAiSuggestion = this.isAiSuggestion(rectangleData);

    console.log('🎯 Starting annotation form:', {
      rectangleData,
      isTrackingAiSuggestion: this.isTrackingAiSuggestion,
      aiFlags: {
        isAiSuggestion: rectangleData?.isAiSuggestion,
        clickType: rectangleData?.clickType,
        suggestionType: rectangleData?.suggestionType
      }
    });

    this.currentReflectionData = {
      annotationId: this.generateId(),
      userId: this.userId,
      sessionId: this.sessionId,
      spectroName: this.localStorage.getItem('spectroName') || 'unknown',
      formOpenedAt: this.currentAnnotationStart,
      bruiteurFieldTime: 0,
      confidenceFieldTime: 0,
      bruiteurChanges: 0,
      confidenceChanges: 0,
      aiSuggestionsShown: this.isTrackingAiSuggestion ? 1 : 0,
      aiSuggestionsAccepted: 0,
      aiSuggestionsRejected: 0,
      aiSuggestionsModified: 0,
      finalBruiteur: '',
      finalConfidence: 0,
      wasSubmitted: false,
      wasCancelled: false,
      wasDeleted: false,
      wasAiSuggestionAccepted: false,
      rectangleData
    };

    this.trackEvent(ClickstreamEventType.FORM_OPENED, {
      elementId: 'annotation-form',
      metadata: { rectangleData }
    });
  }

  public endAnnotationForm(action: 'submitted' | 'cancelled' | 'deleted', finalData?: any): void {
    if (!this.currentReflectionData || !this.currentAnnotationStart) return;

    const endTime = Date.now();
    const totalReflectionTime = endTime - this.currentAnnotationStart;

    // Complete reflection data
    this.currentReflectionData.formClosedAt = endTime;
    this.currentReflectionData.totalReflectionTime = totalReflectionTime;
    this.currentReflectionData.wasSubmitted = action === 'submitted';
    this.currentReflectionData.wasCancelled = action === 'cancelled';
    this.currentReflectionData.wasDeleted = action === 'deleted';

    // Handle AI suggestion acceptance
    if (this.isTrackingAiSuggestion && action === 'submitted') {
      this.currentReflectionData.aiSuggestionsAccepted = 1;
      this.currentReflectionData.wasAiSuggestionAccepted = true;

      // Call the trackAISuggestionAction method for proper event tracking
      this.trackAISuggestionAction('accepted', this.currentReflectionData.rectangleData, finalData);

      console.log('✅ AI Suggestion accepted:', {
        action,
        aiSuggestionsAccepted: this.currentReflectionData.aiSuggestionsAccepted,
        wasAiSuggestionAccepted: this.currentReflectionData.wasAiSuggestionAccepted
      });
    } else if (this.isTrackingAiSuggestion && (action === 'cancelled' || action === 'deleted')) {
      this.currentReflectionData.aiSuggestionsRejected = 1;

      // Call the trackAISuggestionAction method for proper event tracking
      this.trackAISuggestionAction('rejected', this.currentReflectionData.rectangleData, finalData);

      console.log('❌ AI Suggestion rejected:', {
        action,
        aiSuggestionsRejected: this.currentReflectionData.aiSuggestionsRejected
      });
    }

    if (finalData) {
      this.currentReflectionData.finalBruiteur = finalData.bruiteur || '';
      this.currentReflectionData.finalConfidence = finalData.trust || 0;
    }

    // Calculate field-specific times
    const bruiteurInteraction = this.currentFieldInteractions.get('bruiteur');
    const confidenceInteraction = this.currentFieldInteractions.get('trust');

    if (bruiteurInteraction) {
      this.currentReflectionData.bruiteurFieldTime = bruiteurInteraction.duration;
      this.currentReflectionData.bruiteurChanges = bruiteurInteraction.changeCount;
    }

    if (confidenceInteraction) {
      this.currentReflectionData.confidenceFieldTime = confidenceInteraction.duration;
      this.currentReflectionData.confidenceChanges = confidenceInteraction.changeCount;
    }

    // Emit completed reflection data
    const completedReflectionData = this.currentReflectionData as ReflectionTimeData;
    this.reflectionTimeSubject.next(completedReflectionData);

    // Add to session
    this.currentSession?.reflectionTimes.push(completedReflectionData);
    if (this.currentSession) {
      this.currentSession.annotationCount++;
    }

    // Store reflection data locally
    if (this.config.enableLocalStorage) {
      const existingReflectionData = this.localStorage.getItem('reflection_times');
      const allReflectionData = existingReflectionData ? JSON.parse(existingReflectionData) : [];
      allReflectionData.push(completedReflectionData);
      this.localStorage.setItem('reflection_times', JSON.stringify(allReflectionData));
    }

    // Track form close event
    this.trackEvent(
      action === 'submitted' ? ClickstreamEventType.FORM_SUBMITTED :
        action === 'cancelled' ? ClickstreamEventType.FORM_CANCELLED :
          ClickstreamEventType.FORM_CLOSED,
      {
        elementId: 'annotation-form',
        metadata: {
          action,
          reflectionTime: totalReflectionTime,
          finalData
        }
      }
    );

    // Debug: Show current AI event counts
    const aiCounts = this.getAIEventCounts();
    console.log('📊 Current AI Event Counts:', aiCounts);

    // Reset current tracking
    this.currentAnnotationStart = null;
    this.currentReflectionData = null;
    this.currentFieldInteractions.clear();
    this.isTrackingAiSuggestion = false;
  }

  public trackFieldFocus(fieldName: string, currentValue?: any): void {
    const interaction: FieldInteraction = {
      fieldName,
      startTime: Date.now(),
      endTime: 0,
      duration: 0,
      initialValue: currentValue,
      finalValue: currentValue,
      changeCount: 0,
      focusCount: 1
    };

    this.currentFieldInteractions.set(fieldName, interaction);

    this.trackEvent(ClickstreamEventType.FIELD_FOCUSED, {
      elementId: fieldName,
      elementType: 'form-field',
      value: currentValue
    });
  }

  public trackFieldBlur(fieldName: string, finalValue?: any): void {
    const interaction = this.currentFieldInteractions.get(fieldName);
    if (interaction) {
      interaction.endTime = Date.now();
      interaction.duration += interaction.endTime - interaction.startTime;
      interaction.finalValue = finalValue;
    }

    this.trackEvent(ClickstreamEventType.FIELD_BLURRED, {
      elementId: fieldName,
      elementType: 'form-field',
      value: finalValue
    });
  }

  public trackFieldChange(fieldName: string, newValue: any, oldValue?: any): void {
    const interaction = this.currentFieldInteractions.get(fieldName);
    if (interaction) {
      interaction.changeCount++;
      interaction.finalValue = newValue;
    }

    // Track if this is a modification of an AI suggestion
    if (this.isTrackingAiSuggestion && oldValue !== newValue) {
      console.log('🔄 AI Suggestion modified:', {
        fieldName,
        oldValue,
        newValue,
        isTrackingAiSuggestion: this.isTrackingAiSuggestion,
        currentModifiedCount: this.currentReflectionData?.aiSuggestionsModified
      });

      this.trackAiSuggestionModified({field: fieldName, oldValue}, {field: fieldName, newValue});
    }

    this.trackEvent(ClickstreamEventType.FIELD_CHANGED, {
      elementId: fieldName,
      elementType: 'form-field',
      value: newValue,
      metadata: { oldValue, newValue }
    });
  }

  public trackMouseClick(elementId: string, coordinates?: { x: number, y: number }): void {
    this.trackEvent(ClickstreamEventType.MOUSE_CLICK, {
      elementId,
      metadata: { coordinates }
    });
  }

  public trackRectangleDrawing(action: 'start' | 'end', rectangleData?: any): void {
    this.trackEvent(
      action === 'start' ? ClickstreamEventType.RECTANGLE_DRAW_START : ClickstreamEventType.RECTANGLE_DRAW_END,
      {
        elementId: 'canvas',
        metadata: { rectangleData }
      }
    );
  }

  public trackRectangleClick(rectangleData: any): void {
    this.trackEvent(ClickstreamEventType.RECTANGLE_SELECTED, {
      elementId: 'existing-rectangle',
      metadata: { rectangleData }
    });
  }

  // Helper method to detect AI suggestions
  private isAiSuggestion(rectangleData?: any): boolean {
    // Check if the rectangle is from AI suggestions
    // AI rectangles can be identified by checking if they exist in the AI rectangles array
    // or by checking specific metadata properties
    return rectangleData && (
      rectangleData.isAiSuggestion === true ||
        rectangleData.clickType === 'ai_rectangle_interaction' ||
        rectangleData.suggestionType === 'ai_rectangle'
    );
  }

  // AI Suggestion tracking methods
  public trackAiSuggestionShown(suggestionData: any): void {
    // Don't increment here as it's handled in startAnnotationForm
    this.trackEvent(ClickstreamEventType.AI_SUGGESTION_SHOWN, {
      elementId: 'ai-suggestion',
      metadata: { suggestionData }
    });
  }

  public trackAiSuggestionModified(originalSuggestion: any, modifiedData: any): void {
    if (this.currentReflectionData) {
      this.currentReflectionData.aiSuggestionsModified = (this.currentReflectionData.aiSuggestionsModified || 0) + 1;

      // Call the trackAISuggestionAction method for proper event tracking
      // This will create the AI_SUGGESTION_MODIFIED event that analytics use
      this.trackAISuggestionAction('modified', this.currentReflectionData.rectangleData, modifiedData);

      console.log('✅ AI Suggestion modification tracked:', {
        newCount: this.currentReflectionData.aiSuggestionsModified,
        originalSuggestion,
        modifiedData
      });
    }

    // Note: We don't call trackEvent here anymore to avoid duplicate events
    // The trackAISuggestionAction method above already creates the event
  }

  // Track AI helper activation/deactivation
  public trackAIHelperToggle(isActive: boolean): void {
    this.trackEvent(
      isActive ? ClickstreamEventType.AI_HELPER_ACTIVATED : ClickstreamEventType.AI_HELPER_DEACTIVATED,
      {
        elementId: 'ai-helper-toggle',
        elementType: 'toggle-switch',
        value: isActive,
        metadata: {
          toggleState: isActive,
          timestamp: Date.now()
        }
      }
    );
  }

  // AI Rectangle Click tracking
  public trackAIRectangleClick(rectangleData: any): void {
    this.trackEvent(ClickstreamEventType.AI_RECTANGLE_CLICKED, {
      elementId: 'ai-rectangle',
      elementType: 'ai-suggestion-rectangle',
      metadata: {
        rectangleData,
        clickTimestamp: Date.now()
      }
    });
  }

  public trackAISuggestionAction(action: 'accepted' | 'modified' | 'rejected', originalData: any, finalData?: any) {
    const eventType = action === 'accepted' ? ClickstreamEventType.AI_SUGGESTION_ACCEPTED :
      action === 'modified' ? ClickstreamEventType.AI_SUGGESTION_MODIFIED :
        ClickstreamEventType.AI_SUGGESTION_REJECTED;

    console.log('🎯 trackAISuggestionAction called:', {
      action,
      eventType,
      originalData,
      finalData,
      userId: this.userId
    });

    this.trackEvent(eventType, {
      elementId: 'ai-suggestion',
      value: {action, originalData, finalData},
      metadata: {
        originalBruiteur: originalData?.bruiteur,
        originalTrust: originalData?.trust,
        finalBruiteur: finalData?.bruiteur,
        finalTrust: finalData?.trust,
        wasModified: action === 'modified',
      }
    });

    console.log('✅ AI Suggestion event created:', eventType);
  }

  // Debug method to check current AI event counts
  public getAIEventCounts(): any {
    const aiAccepted = this.events.filter(e => e.eventType === ClickstreamEventType.AI_SUGGESTION_ACCEPTED).length;
    const aiModified = this.events.filter(e => e.eventType === ClickstreamEventType.AI_SUGGESTION_MODIFIED).length;
    const aiRejected = this.events.filter(e => e.eventType === ClickstreamEventType.AI_SUGGESTION_REJECTED).length;

    return {
      aiSuggestionsAccepted: aiAccepted,
      aiSuggestionsModified: aiModified,
      aiSuggestionsRejected: aiRejected,
      totalEvents: this.events.length,
      currentReflectionData: this.currentReflectionData
    };
  }

  private flushEvents(): void {
    if (this.events.length === 0) return;

    // Store in localStorage if enabled
    if (this.config.enableLocalStorage) {
      const existingData = this.localStorage.getItem('clickstream_events');
      const allEvents = existingData ? JSON.parse(existingData) : [];
      allEvents.push(...this.events);
      this.localStorage.setItem('clickstream_events', JSON.stringify(allEvents));
    }

    // TODO: Send to API endpoint
    console.log('Flushing clickstream events:', this.events.length);

    // Clear events after flushing
    this.events = [];
  }

  public getCurrentReflectionTime(): number {
    if (!this.currentAnnotationStart) return 0;
    return Date.now() - this.currentAnnotationStart;
  }

  public getSessionData(): AnnotationSession | null {
    return this.currentSession;
  }

  public exportData(): { events: ClickstreamEvent[], session: AnnotationSession | null } {
    return {
      events: [...this.events],
      session: this.currentSession
    };
  }

  public clearData(): void {
    this.events = [];
    this.currentFieldInteractions.clear();
    this.currentAnnotationStart = null;
    this.currentReflectionData = null;

    if (this.config.enableLocalStorage) {
      this.localStorage.removeItem('clickstream_events');
    }
  }
}
