import {Component, ViewChild, ViewChildren} from '@angular/core';
import {DrawingService} from '../../services/drawing.service';
import {ZoomControlService} from "../../services/zoom-control.service";
import {FormsModule} from "@angular/forms";
import {AnnotationService} from "../../services/annotation.service";
import {LocalStorageService} from "../../services/local-storage.service";
import {SpectroComponent} from "../spectro/spectro.component";
import {AnnotationCreatorComponent} from "../annotation-creator/annotation-creator.component";
import {RectangleService} from "../../services/rectangle.service";
import {ClickstreamService} from "../../services/clickstream.service";

@Component({
  selector: 'app-topbar',
  standalone: true,
  imports: [
    FormsModule
  ],
  templateUrl: './topbar.component.html',
  styleUrl: './topbar.component.css'
})
export class TopbarComponent {
  isHelperActive: boolean = false;
  countClickedHelper: number = 0;

  constructor(private drawingService: DrawingService,  private zoomService: ZoomControlService,
              private annotationService: AnnotationService, private locService: LocalStorageService,
              private spectComponent: SpectroComponent,private rectangleService: RectangleService,
              private clickstreamService: ClickstreamService) {}

  enableRectangleDrawing(): void {
    this.zoomService.disableZoom(); // Disable zoom
    this.drawingService.setDrawingMode(true); // Enable rectangle drawing mode
    console.log('je suis actifs ou pas :', this.annotationService.getValueAnnotationVisibleOrNot());
  }

  logSwitchState(): void {
    // Track AI helper toggle
    this.clickstreamService.trackAIHelperToggle(this.isHelperActive);

    setTimeout(() => {}, 10);
    const nameIA = this.locService.getItem("IAName");
    const nameSpectro = this.locService.getItem("spectroName");

    if(this.isHelperActive) {
      this.countClickedHelper++;
      if (nameIA === 'IAName') {
        this.annotationService.getResponseAnnotation(nameSpectro).subscribe(rectangles => {
          // console.log('Result api : ', rectangles.length, rectangles);
          const converted = this.spectComponent.convertRectangleHelpers(rectangles, true, true)
          // console.log('I am converted : ',converted);
          this.rectangleService.setAIRectangles(converted);
          this.spectComponent.redrawCanvas();
        })
      } else {
        this.annotationService.getRectangles(nameIA, nameSpectro).subscribe(rectangles => {
          this.rectangleService.setAIRectangles(this.spectComponent.convertRectangleHelpers(rectangles, true, true));
          this.spectComponent.redrawCanvas();
        })
      }

      // setTimeout(() => {
      //   this.isHelperActive = false;
      //   this.rectangleService.clearAiRectangles();
      //   this.spectComponent.closeAnnotationCreator();
      //   this.spectComponent.initializeCanvas();
      // }, 15000);

      console.log(`Count number of clicked ; ${this.countClickedHelper}`)

    } else {
      this.spectComponent.initializeCanvas();
      console.log("Ai Rectangles before cleaning:", this.rectangleService.getAiRectangles());
      this.rectangleService.clearAiRectangles();
      console.log("Ai Rectangles after cleaning:", this.rectangleService.getAiRectangles());

      console.log("Current rectangle exisitng ? : ", this.spectComponent.currentRectangle)
    }
  }
}
