import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ClickstreamService } from '../../services/clickstream.service';
import { ClickstreamStorageService } from '../../services/clickstream-storage.service';
import { LocalStorageService } from '../../services/local-storage.service';
import {
  ReflectionTimeData,
  ClickstreamAnalytics,
  AnnotationSession
} from '../../models/clickstream.models';

@Component({
  selector: 'app-reflection-analytics',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './reflection-analytics.component.html',
  styleUrl: './reflection-analytics.component.css'
})
export class ReflectionAnalyticsComponent implements OnInit {
  analytics: ClickstreamAnalytics | null = null;
  reflectionTimes: ReflectionTimeData[] = [];
  currentSession: AnnotationSession | null = null;
  userId: string = '';
  spectroName: string = '';

  // Real-time tracking
  currentReflectionTime: number = 0;
  isFormOpen: boolean = false;

  // Display options
  showDetailedData: boolean = false;
  showRealTimeTracking: boolean = true;

  constructor(
    private clickstreamService: ClickstreamService,
    private storageService: ClickstreamStorageService,
    private localStorage: LocalStorageService
  ) {}

  ngOnInit(): void {
    this.userId = this.localStorage.getItem('uniqueId') || 'anonymous';
    this.spectroName = this.localStorage.getItem('spectroName') || 'unknown';

    this.loadAnalytics();
    this.setupRealTimeTracking();
  }

  private loadAnalytics(): void {
    // Load local analytics
    this.analytics = this.storageService.calculateLocalAnalytics(this.userId, this.spectroName);
    this.reflectionTimes = this.storageService.getLocalReflectionTimes()
      .filter(rt => rt.userId === this.userId && rt.spectroName === this.spectroName);
    this.currentSession = this.clickstreamService.getSessionData();
  }

  private setupRealTimeTracking(): void {
    // Subscribe to reflection time updates
    this.clickstreamService.reflectionTimes$.subscribe(reflectionData => {
      this.reflectionTimes.push(reflectionData);
      this.loadAnalytics(); // Refresh analytics
    });

    // Track current reflection time if form is open
    setInterval(() => {
      if (this.showRealTimeTracking) {
        this.currentReflectionTime = this.clickstreamService.getCurrentReflectionTime();
        this.isFormOpen = this.currentReflectionTime > 0;
      }
    }, 100);
  }

  formatTime(milliseconds: number): string {
    if (milliseconds === 0) return '0s';

    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  formatTimeShort(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);

    if (minutes > 0) {
      return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
    } else {
      return `${seconds}s`;
    }
  }

  getEfficiencyColor(hesitationScore: number): string {
    if (hesitationScore < 5) return '#4CAF50'; // Green - efficient
    if (hesitationScore < 15) return '#FF9800'; // Orange - moderate
    return '#F44336'; // Red - needs improvement
  }

  getReflectionTimeColor(time: number): string {
    if (!this.analytics) return '#666';

    const avgTime = this.analytics.averageReflectionTime;
    if (time < avgTime * 0.8) return '#4CAF50'; // Fast
    if (time < avgTime * 1.2) return '#FF9800'; // Average
    return '#F44336'; // Slow
  }

  exportData(): void {
    const data = {
      analytics: this.analytics,
      reflectionTimes: this.reflectionTimes,
      session: this.currentSession,
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `reflection-analytics-${this.userId}-${this.spectroName}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  syncToServer(): void {
    this.storageService.syncLocalDataToServer().subscribe({
      next: (response) => {
        console.log('Data synced successfully:', response);
        alert('Data synced to server successfully!');
      },
      error: (error) => {
        console.error('Failed to sync data:', error);
        alert('Failed to sync data to server. Data is saved locally.');
      }
    });
  }

  clearData(): void {
    if (confirm('Are you sure you want to clear all analytics data? This cannot be undone.')) {
      this.storageService.clearAllLocalData();
      this.clickstreamService.clearData();
      this.loadAnalytics();
      alert('All data cleared successfully.');
    }
  }

  toggleDetailedView(): void {
    this.showDetailedData = !this.showDetailedData;
  }

  toggleRealTimeTracking(): void {
    this.showRealTimeTracking = !this.showRealTimeTracking;
  }

  getLastAnnotationTime(): string {
    if (this.reflectionTimes.length === 0) return 'No annotations yet';

    const lastAnnotation = this.reflectionTimes[this.reflectionTimes.length - 1];
    const timeDiff = Date.now() - lastAnnotation.formClosedAt;

    if (timeDiff < 60000) return 'Just now';
    if (timeDiff < 3600000) return `${Math.floor(timeDiff / 60000)} minutes ago`;
    return `${Math.floor(timeDiff / 3600000)} hours ago`;
  }

  getAnnotationTrend(): string {
    if (this.reflectionTimes.length < 3) return 'Not enough data';

    const recent = this.reflectionTimes.slice(-3);
    const times = recent.map(rt => rt.totalReflectionTime);

    const isImproving = times[2] < times[1] && times[1] < times[0];
    const isGettingSlower = times[2] > times[1] && times[1] > times[0];

    if (isImproving) return 'Getting faster ⬇️';
    if (isGettingSlower) return 'Getting slower ⬆️';
    return 'Stable ➡️';
  }

  // Data management methods
  getLocalStorageSize(): string {
    const sizeInMB = this.clickstreamService.getLocalStorageSize();
    return sizeInMB.toFixed(2);
  }
}
