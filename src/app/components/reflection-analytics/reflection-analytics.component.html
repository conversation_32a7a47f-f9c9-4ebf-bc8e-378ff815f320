<div class="analytics-container">
  <div class="header">
    <h2>Reflection Time Analytics</h2>
    <div class="header-actions">
      <button (click)="toggleRealTimeTracking()" [class.active]="showRealTimeTracking">
        Real-time Tracking
      </button>
      <button (click)="toggleDetailedView()" [class.active]="showDetailedData">
        Detailed View
      </button>
      <button (click)="exportData()" class="export-btn">Export Data</button>
      <button (click)="syncToServer()" class="sync-btn">Sync to Server</button>
    </div>
  </div>

  <!-- Real-time Tracking -->
  <div *ngIf="showRealTimeTracking" class="real-time-section">
    <h3>Current Session</h3>
    <div class="real-time-stats">
      <div class="stat-card" [class.active]="isFormOpen">
        <div class="stat-label">Current Reflection Time</div>
        <div class="stat-value">{{ formatTimeShort(currentReflectionTime) }}</div>
        <div class="stat-status" *ngIf="isFormOpen">Form is open</div>
        <div class="stat-status inactive" *ngIf="!isFormOpen">No active annotation</div>
      </div>

      <div class="stat-card">
        <div class="stat-label">Session Annotations</div>
        <div class="stat-value">{{ currentSession?.annotationCount || 0 }}</div>
        <div class="stat-status">{{ getLastAnnotationTime() }}</div>
      </div>

      <div class="stat-card">
        <div class="stat-label">Trend</div>
        <div class="stat-value">{{ getAnnotationTrend() }}</div>
      </div>
    </div>
  </div>

  <!-- Summary Analytics -->
  <div class="summary-section" *ngIf="analytics">
    <h3>Session Summary</h3>
    <div class="summary-grid">
      <div class="summary-card">
        <div class="summary-title">Total Annotations</div>
        <div class="summary-value">{{ analytics.totalAnnotations }}</div>
      </div>

      <div class="summary-card">
        <div class="summary-title">Average Reflection Time</div>
        <div class="summary-value">{{ formatTime(analytics.averageReflectionTime) }}</div>
      </div>

      <div class="summary-card">
        <div class="summary-title">Efficiency Score</div>
        <div class="summary-value" [style.color]="getEfficiencyColor(analytics.hesitationScore)">
          {{ (10 - Math.min(analytics.hesitationScore, 10)).toFixed(1) }}/10
        </div>
      </div>

      <div class="summary-card">
        <div class="summary-title">Annotations/Minute</div>
        <div class="summary-value">{{ analytics.annotationsPerMinute.toFixed(1) }}</div>
      </div>
    </div>
  </div>

  <!-- Detailed Analytics -->
  <div *ngIf="showDetailedData && analytics" class="detailed-section">
    <h3>Detailed Analytics</h3>

    <div class="analytics-grid">
      <div class="analytics-card">
        <h4>Time Distribution</h4>
        <div class="time-stats">
          <div class="time-stat">
            <span>Median:</span>
            <span>{{ formatTime(analytics.medianReflectionTime) }}</span>
          </div>
          <div class="time-stat">
            <span>Fastest:</span>
            <span>{{ formatTime(analytics.minReflectionTime) }}</span>
          </div>
          <div class="time-stat">
            <span>Slowest:</span>
            <span>{{ formatTime(analytics.maxReflectionTime) }}</span>
          </div>
        </div>
      </div>

      <div class="analytics-card">
        <h4>Field Interaction</h4>
        <div class="field-stats">
          <div class="field-stat">
            <span>Avg. Noise Type Time:</span>
            <span>{{ formatTime(analytics.averageBruiteurTime) }}</span>
          </div>
          <div class="field-stat">
            <span>Avg. Confidence Time:</span>
            <span>{{ formatTime(analytics.averageConfidenceTime) }}</span>
          </div>
          <div class="field-stat">
            <span>Avg. Changes per Annotation:</span>
            <span>{{ analytics.averageChangesPerAnnotation.toFixed(1) }}</span>
          </div>
        </div>
      </div>

      <div class="analytics-card">
        <h4>Common Choices</h4>
        <div class="choice-stats">
          <div class="choice-stat">
            <span>Most Common Noise Type:</span>
            <span class="choice-value">{{ analytics.mostCommonBruiteur || 'N/A' }}</span>
          </div>
          <div class="choice-stat">
            <span>Most Common Confidence:</span>
            <span class="choice-value">{{ analytics.mostCommonConfidence }}%</span>
          </div>
        </div>
      </div>


      <div class="analytics-card ai-analytics">
        <h4>AI Assistant Analytics</h4>
        <div class="ai-stats">
          <div class="ai-stat">
            <span>AI Helper Activations:</span>
            <span class="ai-value">{{ analytics.aiHelperUsageCount }}</span>
          </div>
          <div class="ai-stat">
            <span>AI Rectangle Clicks:</span>
            <span class="ai-value">{{ analytics.aiRectangleClickCount }}</span>
          </div>
          <div class="ai-stat">
            <span>AI Suggestions Accepted:</span>
            <span class="ai-value success">{{ analytics.aiSuggestionsAccepted }}</span>
          </div>
          <div class="ai-stat">
            <span>AI Suggestions Modified:</span>
            <span class="ai-value warning">{{ analytics.aiSuggestionsModified }}</span>
          </div>
          <div class="ai-stat">
            <span>AI Suggestions Rejected:</span>
            <span class="ai-value error">{{ analytics.aiSuggestionsRejected }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- AI Performance Metrics -->
    <div class="ai-performance-section" *ngIf="analytics.totalAIInteractions > 0">
      <h4>AI Performance Rates</h4>
      <div class="performance-grid">
        <div class="performance-card acceptance">
          <div class="performance-title">Acceptance Rate</div>
          <div class="performance-value">{{ analytics.aiAcceptanceRate.toFixed(1) }}%</div>
          <div class="performance-bar">
            <div class="performance-fill" [style.width.%]="analytics.aiAcceptanceRate"></div>
          </div>
        </div>

        <div class="performance-card modification">
          <div class="performance-title">Modification Rate</div>
          <div class="performance-value">{{ analytics.aiModificationRate.toFixed(1) }}%</div>
          <div class="performance-bar">
            <div class="performance-fill" [style.width.%]="analytics.aiModificationRate"></div>
          </div>
        </div>

        <div class="performance-card rejection">
          <div class="performance-title">Rejection Rate</div>
          <div class="performance-value">{{ analytics.aiRejectionRate.toFixed(1) }}%</div>
          <div class="performance-bar">
            <div class="performance-fill" [style.width.%]="analytics.aiRejectionRate"></div>
          </div>
        </div>

        <div class="performance-card reflection-time">
          <div class="performance-title">Avg. AI Reflection Time</div>
          <div class="performance-value">{{ formatTime(analytics.averageAIReflectionTime) }}</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Individual Annotations -->
  <div *ngIf="showDetailedData && reflectionTimes.length > 0" class="annotations-section">
    <h3>Individual Annotations ({{ reflectionTimes.length }})</h3>
    <div class="annotations-list">
      <div *ngFor="let annotation of reflectionTimes; let i = index"
           class="annotation-item"
           [style.border-left-color]="getReflectionTimeColor(annotation.totalReflectionTime)">
        <div class="annotation-header">
          <span class="annotation-number">#{{ i + 1 }}</span>
          <span class="annotation-time">{{ formatTime(annotation.totalReflectionTime) }}</span>
          <span class="annotation-status"
                [class.submitted]="annotation.wasSubmitted"
                [class.cancelled]="annotation.wasCancelled"
                [class.deleted]="annotation.wasDeleted">
            {{ annotation.wasSubmitted ? 'Submitted' :
            annotation.wasCancelled ? 'Cancelled' :
              annotation.wasDeleted ? 'Deleted' : 'Unknown' }}
          </span>
        </div>
        <div class="annotation-details">
          <div class="annotation-detail">
            <strong>Type:</strong> {{ annotation.finalBruiteur || 'Not set' }}
          </div>
          <div class="annotation-detail">
            <strong>Confidence:</strong> {{ annotation.finalConfidence }}%
          </div>
          <div class="annotation-detail">
            <strong>Changes:</strong> {{ annotation.bruiteurChanges + annotation.confidenceChanges }}
          </div>
          <div class="annotation-detail">
            <strong>Field Times:</strong>
            Type: {{ formatTimeShort(annotation.bruiteurFieldTime) }},
            Confidence: {{ formatTimeShort(annotation.confidenceFieldTime) }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- No Data State -->
  <div *ngIf="!analytics || analytics.totalAnnotations === 0" class="no-data">
    <h3>No Analytics Data Available</h3>
    <p>Start creating annotations to see reflection time analytics.</p>
  </div>

  <!-- Actions -->
  <div class="actions-section">
    <button (click)="clearData()" class="danger-btn">Clear All Data</button>
  </div>
</div>
