import {Component, OnInit} from '@angular/core';
import {SelectingMissionService} from "../../services/selecting-mission.service";
import {FormsModule} from "@angular/forms";
import {NgForOf, NgIf} from "@angular/common";
import {LocalStorageService} from "../../services/local-storage.service";
import {Router} from "@angular/router";
import {AudioService} from "../../services/audio.service";


@Component({
  selector: 'app-selecting-mission',
  standalone: true,
  imports: [
    FormsModule,
    NgForOf,
    NgIf
  ],
  templateUrl: './selecting-mission.component.html',
  styleUrl: './selecting-mission.component.css'
})
export class SelectingMissionComponent implements OnInit {
  spectroNames: string[] = [];
  selectedSpectroNames: string = '';

  userNamesChoose: string[] = [];
  selectNameChoose: string = '';
  sourceType: 'user' | 'ia' = 'ia';

  constructor(private selectingMissionService: SelectingMissionService, private localstorageService: LocalStorageService,
              private router: Router, private audioService: AudioService) {}

  ngOnInit() {
    this.selectingMissionService.getAllSpectroNames().subscribe((data) => {
      this.spectroNames = data;
    })
  }

  onImageSelect() {
    this.selectingMissionService.get_user_who_made_annotation_on_specific_spectro(this.selectedSpectroNames).subscribe((data) => {
      this.userNamesChoose = data;
      this.selectNameChoose = this.userNamesChoose.length > 0 ? this.userNamesChoose[0] : '';
    }, (err) => {
      console.log("Error fetching users from selecting mission : ",err);
      this.userNamesChoose = [];
      this.selectNameChoose = '';
    })
  }

  saveSpectroName() {
    this.localstorageService.setItem('spectroName', this.selectedSpectroNames);

    if(this.sourceType === 'ia') {
      this.localstorageService.setItem('IAName', 'IAName');
      alert(`Saved : ${this.selectedSpectroNames} with IA`);
    } else if(this.sourceType === 'user') {
      this.localstorageService.setItem('IAName', this.selectNameChoose);
      alert(`Saved : ${this.selectedSpectroNames} with user ${this.selectNameChoose}`);
    }

    // if(this.selectedSpectroNames && this.selectNameChoose) {
    //   this.localstorageService.setItem('spectroName', this.selectedSpectroNames);
    //   this.localstorageService.setItem('IAName', this.selectNameChoose);
    //   alert(`Saved : ${this.selectedSpectroNames} ${this.selectNameChoose}`);
    // }
  }

  goToHome() {
    this.router.navigate(['/']);
  }

}
