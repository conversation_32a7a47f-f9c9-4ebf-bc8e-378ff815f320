<label for="spectroNameSelect">Select une mission : </label>
<select id="spectroNameSelect" [(ngModel)]="selectedSpectroNames" (change)="onImageSelect()">
  <option *ngFor="let spectroName of spectroNames" [value]="spectroName">
    {{ spectroName}}
  </option>
</select>
<br>

<label>
  <input type="radio" name="sourceType" value="ia" [(ngModel)]="sourceType"> Ia
</label>
<label>
  <input type="radio" name="sourceType" value="user" [(ngModel)]="sourceType"> Utilisateur
</label>

<div *ngIf="sourceType === 'user' && userNamesChoose.length > 0">
  <label for="userNameSelect">Select un utilisateur IA : </label>
  <select id="userNameSelect" [(ngModel)]="selectNameChoose">
    <option *ngFor="let userChooseName of userNamesChoose" [value]="userChooseName">
      {{ userChooseName}}
    </option>
  </select>
</div>


<br>
<button (click)="saveSpectroName()">Save</button>
<br>
<br>
<button (click)="goToHome()">Go to home</button>
