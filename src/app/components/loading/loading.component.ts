import {Component, OnInit} from '@angular/core';
import {Router} from "@angular/router";
import {AttribuSpectro, InfoSpectroService} from "../../services/info-spectro.service";
import {NgForOf, NgIf} from "@angular/common";
import {FinishTimeService, IFinishTime} from "../../services/finish-time.service";
import {LocalStorageService} from "../../services/local-storage.service";


@Component({
  selector: 'app-loading',
  standalone: true,
  imports: [
    NgIf
  ],
  templateUrl: './loading.component.html',
  styleUrl: './loading.component.css'
})
export class LoadingComponent implements OnInit {
  data!: AttribuSpectro;
  timeTotal!: string;

  constructor(private router: Router, private spectroService: InfoSpectroService, private finishTimeService: FinishTimeService,
              private localstorage: LocalStorageService) {}

  goToSpectro() {
    this.router.navigate(['/spectro'])
  }

  ngOnInit() {
    const nameSpectro = this.localstorage.getItem('spectroName')

    this.spectroService.getInfoSpectro(nameSpectro).subscribe(
      (response: AttribuSpectro) => {
        this.data = response;
      }, err => {
        console.log(err);
      })

    this.finishTimeService.getFinishTime(nameSpectro).subscribe(result => {
      this.timeTotal = result;
    })
  }


}
