import {ChangeDetectorRef, Component, OnDestroy, OnInit} from '@angular/core';
import {interval, Subscription} from "rxjs";
import {FinishTimeService, IFinishTime} from "../../services/finish-time.service";
import {LocalStorageService} from "../../services/local-storage.service";
import {Router} from "@angular/router";



@Component({
  selector: 'app-timer',
  standalone: true,
  imports: [],
  templateUrl: './timer.component.html',
  styleUrl: './timer.component.css'
})
export class TimerComponent implements OnInit, OnDestroy {
  remainingTime: number = 0;
  timeTotal: number = 0 ;
  private timerSubscription?: Subscription;
  spectroName: string | null= '';

  constructor(private finishTimeService: FinishTimeService, private localService : LocalStorageService,
              private cdRef: ChangeDetectorRef, private router: Router) {}

  ngOnInit() {
    const nameSpectro = this.localService.getItem('spectroName');
    const savedTime = this.localService.getItem('remainingTime');
    const savecTotalTime = this.localService.getItem('timeTotal');

    if (savedTime != null && savecTotalTime != null) {
      this.remainingTime = parseInt(savedTime, 10);
      this.timeTotal = parseInt(savecTotalTime, 10);
      this.startTimer();
    } else if (nameSpectro) {
      this.finishTimeService.getFinishTime(nameSpectro).subscribe({
        next: (reponse) => {
          this.remainingTime = this.convertTimeToSeconds(reponse)

          if(!savecTotalTime) {
            this.timeTotal = this.remainingTime;
            this.localService.setItem('timeTotal', this.timeTotal.toString());
          }

          this.localService.setItem('remainingTime', this.remainingTime.toString()) // inital value
          this.startTimer();
        },
        error: (error) => console.log('Error fetching finish time', error)
      });
    }
  }

  convertTimeToSeconds(timeString: string): number {
    const [hours, minutes, seconds] = timeString.split(':').map(Number);
    return hours*36000 + minutes * 60 + seconds;
  }

  getFormattedTime(seconds: number): string {
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2 ,'0')
    ].join(':');
  }

  startTimer(): void {
    this.timerSubscription = interval(1000).subscribe(() => {
      if (this.remainingTime > 0) {
        this.remainingTime--;
        this.localService.setItem('remainingTime', this.remainingTime.toString())
        this.cdRef.detectChanges();
      } else {
        this.timerSubscription?.unsubscribe();
        this.router.navigate(['/score']);
        this.localService.removeItem('remainingTime');
        this.localService.removeItem('timeTotal');
      }
  });
  }

  ngOnDestroy(): void {
    this.timerSubscription?.unsubscribe();
  }

  finishTimer(): void {
    this.timerSubscription?.unsubscribe();
    this.router.navigate(['/score']);
    this.localService.removeItem('remainingTime');
    this.localService.removeItem('timeTotal');
  }
}
